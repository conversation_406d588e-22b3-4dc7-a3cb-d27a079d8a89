package com.cdz360.iot.gw.south.server;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * ChargeHbService 的 getPriceByTime 方法单元测试
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ChargeHbServiceTest {

    private ChargeHbService chargeHbService;
    private ChargePriceVo testPriceVo;

    @BeforeEach
    void setUp() {
        chargeHbService = new ChargeHbService();
        testPriceVo = createTestPriceVo();
    }

    /**
     * 创建测试用的价格数据 基于用户提供的JSON数据
     */
    private ChargePriceVo createTestPriceVo() {
        String jsonStr = "{\"id\":1101,\"enable\":false,\"itemList\":[{\"code\":4,\"startTime\":\"00:00\",\"endTime\":\"13:30\",\"elecPrice\":0.5433,\"servPrice\":0.4566},{\"code\":3,\"startTime\":\"13:30\",\"endTime\":\"14:00\",\"elecPrice\":0.7722,\"servPrice\":0.4566},{\"code\":2,\"startTime\":\"14:00\",\"endTime\":\"14:30\",\"elecPrice\":0.9233,\"servPrice\":0.4566},{\"code\":1,\"startTime\":\"14:30\",\"endTime\":\"15:00\",\"elecPrice\":1.025,\"servPrice\":0.4566},{\"code\":4,\"startTime\":\"15:00\",\"endTime\":\"15:30\",\"elecPrice\":0.5433,\"servPrice\":0.4566},{\"code\":3,\"startTime\":\"15:30\",\"endTime\":\"16:00\",\"elecPrice\":0.7722,\"servPrice\":0.4566},{\"code\":2,\"startTime\":\"16:00\",\"endTime\":\"16:30\",\"elecPrice\":0.9233,\"servPrice\":0.4566},{\"code\":1,\"startTime\":\"16:30\",\"endTime\":\"17:00\",\"elecPrice\":1.025,\"servPrice\":0.4566},{\"code\":4,\"startTime\":\"17:00\",\"endTime\":\"24:00\",\"elecPrice\":0.5433,\"servPrice\":0.4566}]}";
        return JsonUtils.fromJson(jsonStr, ChargePriceVo.class);
    }

    private ChargePriceItem createPriceItem(int code, String startTime, String endTime,
        double elecPrice, double servPrice) {
        ChargePriceItem item = new ChargePriceItem();
        item.setCode(code);
        item.setStartTime(startTime);
        item.setEndTime(endTime);
        item.setElecPrice(BigDecimal.valueOf(elecPrice));
        item.setServPrice(BigDecimal.valueOf(servPrice));
        return item;
    }

    @Test
    @DisplayName("测试null输入参数")
    void testGetPriceByTime_NullInputs() {
        // 测试时间为null的情况
        ChargePriceItem result = invokeGetPriceByTime(null, testPriceVo);
        assertNull(result, "当时间参数为null时应返回null");

        // 测试价格信息为null的情况
        ZonedDateTime testTime = ZonedDateTime.of(2023, 12, 25, 10, 0, 0, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(testTime, null);
        assertNull(result, "当价格信息为null时应返回null");

        // 测试价格列表为空的情况
        ChargePriceVo emptyPriceVo = new ChargePriceVo();
        emptyPriceVo.setItemList(new ArrayList<>());
        result = invokeGetPriceByTime(testTime, emptyPriceVo);
        assertNull(result, "当价格列表为空时应返回null");
    }

    @Test
    @DisplayName("测试正常时间段匹配")
    void testGetPriceByTime_NormalTimeMatching() {
        // 测试早上8点（在00:00-13:30时段内）
        ZonedDateTime morningTime = ZonedDateTime.of(2023, 12, 25, 8, 0, 0, 0,
            ZoneId.systemDefault());
        ChargePriceItem result = invokeGetPriceByTime(morningTime, testPriceVo);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(4, result.getCode(), "价格代码应该是4");
        assertEquals(BigDecimal.valueOf(0.5433), result.getElecPrice(), "电费应该是0.5433");
        assertEquals(BigDecimal.valueOf(0.4566), result.getServPrice(), "服务费应该是0.4566");
        assertEquals("00:00", result.getStartTime(), "开始时间应该是00:00");
        assertEquals("13:30", result.getEndTime(), "结束时间应该是13:30");

        // 测试下午2点10分（在14:00-14:30时段内）
        ZonedDateTime afternoonTime = ZonedDateTime.of(2023, 12, 25, 14, 10, 0, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(afternoonTime, testPriceVo);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(2, result.getCode(), "价格代码应该是2");
        assertEquals(BigDecimal.valueOf(0.9233), result.getElecPrice(), "电费应该是0.9233");
        assertEquals(BigDecimal.valueOf(0.4566), result.getServPrice(), "服务费应该是0.4566");
        assertEquals("14:00", result.getStartTime(), "开始时间应该是14:00");
        assertEquals("14:30", result.getEndTime(), "结束时间应该是14:30");
    }

    @Test
    @DisplayName("测试边界时间匹配")
    void testGetPriceByTime_BoundaryTimeMatching() {
        // 测试13:30整点（时段切换点，应该匹配13:30-14:00时段）
        ZonedDateTime boundaryTime1 = ZonedDateTime.of(2023, 12, 25, 13, 30, 0, 0,
            ZoneId.systemDefault());
        ChargePriceItem result = invokeGetPriceByTime(boundaryTime1, testPriceVo);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(3, result.getCode(), "在13:30整点应该匹配code=3的时段");
        assertEquals("13:30", result.getStartTime(), "开始时间应该是13:30");
        assertEquals("14:00", result.getEndTime(), "结束时间应该是14:00");

        // 测试14:00整点（时段切换点，应该匹配14:00-14:30时段）
        ZonedDateTime boundaryTime2 = ZonedDateTime.of(2023, 12, 25, 14, 0, 0, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(boundaryTime2, testPriceVo);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(2, result.getCode(), "在14:00整点应该匹配code=2的时段");
        assertEquals("14:00", result.getStartTime(), "开始时间应该是14:00");
        assertEquals("14:30", result.getEndTime(), "结束时间应该是14:30");

        // 测试13:29（应该还在00:00-13:30时段内）
        ZonedDateTime beforeBoundary = ZonedDateTime.of(2023, 12, 25, 13, 29, 59, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(beforeBoundary, testPriceVo);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(4, result.getCode(), "在13:29应该还在code=4的时段");
        assertEquals("00:00", result.getStartTime(), "开始时间应该是00:00");
        assertEquals("13:30", result.getEndTime(), "结束时间应该是13:30");
    }

    @Test
    @DisplayName("测试夜间时间段")
    void testGetPriceByTime_NightTimeMatching() {
        // 测试晚上10点（在17:00-24:00时段内）
        ZonedDateTime nightTime = ZonedDateTime.of(2023, 12, 25, 22, 0, 0, 0,
            ZoneId.systemDefault());
        ChargePriceItem result = invokeGetPriceByTime(nightTime, testPriceVo);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(4, result.getCode(), "晚上10点应该匹配code=4的时段");
        assertEquals("17:00", result.getStartTime(), "开始时间应该是17:00");
        assertEquals("24:00", result.getEndTime(), "结束时间应该是24:00");

        // 测试午夜（00:00，应该匹配00:00-13:30时段）
        ZonedDateTime midnightTime = ZonedDateTime.of(2023, 12, 25, 0, 0, 0, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(midnightTime, testPriceVo);
        System.out.println("result = " + result);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(4, result.getCode(), "午夜应该匹配code=4的时段");
        assertEquals("00:00", result.getStartTime(), "开始时间应该是00:00");
        assertEquals("13:30", result.getEndTime(), "结束时间应该是13:30");

        // 测试午夜（23:59，应该匹配17:00-24:00时段）
        ZonedDateTime nightTime2 = ZonedDateTime.of(2023, 12, 25, 23, 59, 10, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(nightTime2, testPriceVo);
        System.out.println("result = " + result);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(4, result.getCode(), "23:59应该匹配code=4的时段");
        assertEquals("17:00", result.getStartTime(), "开始时间应该是17:00");
        assertEquals("24:00", result.getEndTime(), "结束时间应该是24:00");
    }

    @Test
    @DisplayName("测试峰值时段匹配")
    void testGetPriceByTime_PeakTimeMatching() {
        // 测试14:45（在14:30-15:00时段内，最高价时段）
        ZonedDateTime peakTime = ZonedDateTime.of(2023, 12, 25, 14, 45, 0, 0,
            ZoneId.systemDefault());
        ChargePriceItem result = invokeGetPriceByTime(peakTime, testPriceVo);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(1, result.getCode(), "14:45应该匹配code=1的时段（最高价）");
        assertEquals(BigDecimal.valueOf(1.025), result.getElecPrice(), "电费应该是1.025（最高价）");
        assertEquals("14:30", result.getStartTime(), "开始时间应该是14:30");
        assertEquals("15:00", result.getEndTime(), "结束时间应该是15:00");

        // 测试16:45（在16:30-17:00时段内，也是最高价时段）
        ZonedDateTime peakTime2 = ZonedDateTime.of(2023, 12, 25, 16, 45, 0, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(peakTime2, testPriceVo);

        assertNotNull(result, "应该找到匹配的价格信息");
        assertEquals(1, result.getCode(), "16:45应该匹配code=1的时段（最高价）");
        assertEquals(BigDecimal.valueOf(1.025), result.getElecPrice(), "电费应该是1.025（最高价）");
    }

    @Test
    @DisplayName("测试价格项时间格式无效时的处理")
    void testGetPriceByTime_InvalidTimeFormat() {
        // 创建包含无效时间格式的价格信息
        ChargePriceVo invalidPriceVo = new ChargePriceVo();
        List<ChargePriceItem> itemList = new ArrayList<>();

        // 添加无效时间格式的价格项
        ChargePriceItem invalidItem = new ChargePriceItem();
        invalidItem.setCode(1);
        invalidItem.setStartTime("invalid");
        invalidItem.setEndTime("25:00");
        invalidItem.setElecPrice(BigDecimal.valueOf(1.0));
        invalidItem.setServPrice(BigDecimal.valueOf(0.5));
        itemList.add(invalidItem);

        // 添加有效的价格项
        itemList.add(createPriceItem(2, "10:00", "12:00", 0.8, 0.4));

        invalidPriceVo.setItemList(itemList);

        // 测试时间在有效价格项范围内
        ZonedDateTime testTime = ZonedDateTime.of(2023, 12, 25, 11, 0, 0, 0,
            ZoneId.systemDefault());
        ChargePriceItem result = invokeGetPriceByTime(testTime, invalidPriceVo);

        assertNotNull(result, "应该找到有效的价格信息");
        assertEquals(2, result.getCode(), "应该匹配有效的价格项");

        // 测试时间不在任何有效价格项范围内
        ZonedDateTime outsideTime = ZonedDateTime.of(2023, 12, 25, 15, 0, 0, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(outsideTime, invalidPriceVo);

        assertNull(result, "当时间不在任何有效价格项范围内时应返回null");
    }

    @Test
    @DisplayName("测试跨天价格时段")
    void testGetPriceByTime_CrossDayTimeRange() {
        // 创建包含跨天时段的价格信息
        ChargePriceVo crossDayPriceVo = new ChargePriceVo();
        List<ChargePriceItem> itemList = new ArrayList<>();

        // 跨天时段：23:00-01:00
        itemList.add(createPriceItem(1, "23:00", "01:00", 0.6, 0.3));
        // 正常时段：01:00-18:00
        itemList.add(createPriceItem(2, "01:00", "18:00", 0.8, 0.4));

        crossDayPriceVo.setItemList(itemList);

        // 测试23:30（应该在跨天时段内）
        ZonedDateTime lateNight = ZonedDateTime.of(2023, 12, 25, 23, 30, 0, 0,
            ZoneId.systemDefault());
        ChargePriceItem result = invokeGetPriceByTime(lateNight, crossDayPriceVo);
        System.out.println("result1 = " + result);

        assertNotNull(result, "应该找到跨天时段的价格信息");
        assertEquals(1, result.getCode(), "23:30应该匹配跨天时段");
        assertEquals("23:00", result.getStartTime());
        assertEquals("01:00", result.getEndTime());

        // 测试00:30（应该在跨天时段内）
        ZonedDateTime earlyMorning = ZonedDateTime.of(2023, 12, 25, 0, 30, 0, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(earlyMorning, crossDayPriceVo);
        System.out.println("result2 = " + result);

        assertNotNull(result, "应该找到跨天时段的价格信息");
        assertEquals(1, result.getCode(), "00:30应该匹配跨天时段");

        // 测试01:00
        ZonedDateTime earlyMorning2 = ZonedDateTime.of(2023, 12, 25, 1, 0, 0, 0,
            ZoneId.systemDefault());
        result = invokeGetPriceByTime(earlyMorning2, crossDayPriceVo);
        System.out.println("result3 = " + result);

        assertNotNull(result, "应该找到正常时段的价格信息");
        assertEquals(2, result.getCode(), "01:00应该匹配正常时段");

        // 测试12:00（应该在正常时段内）
        ZonedDateTime noon = ZonedDateTime.of(2023, 12, 25, 12, 0, 0, 0, ZoneId.systemDefault());
        result = invokeGetPriceByTime(noon, crossDayPriceVo);
        System.out.println("result4 = " + result);

        assertNotNull(result, "应该找到正常时段的价格信息");
        assertEquals(2, result.getCode(), "12:00应该匹配正常时段");
    }

    /**
     * 通过反射调用私有方法 getPriceByTime
     */
    private ChargePriceItem invokeGetPriceByTime(ZonedDateTime mvHbTime, ChargePriceVo priceVo) {
        return (ChargePriceItem) ReflectionTestUtils.invokeMethod(chargeHbService, "getPriceByTime",
            mvHbTime, priceVo);
    }
} 